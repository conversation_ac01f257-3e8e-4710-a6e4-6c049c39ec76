package com.example.floatingai;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.os.Bundle;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.Toast;

import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.net.Uri;

public class AreaSelectionActivity extends Activity {
    
    private static final String TAG = "AreaSelectionActivity";
    
    private ImageView imageView;
    private SelectionOverlay selectionOverlay;
    private String fullScreenPath;
    private String userMessage;
    private Bitmap fullScreenBitmap;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Configurar la actividad para pantalla completa sin desplazamientos
        getWindow().setFlags(
            android.view.WindowManager.LayoutParams.FLAG_FULLSCREEN,
            android.view.WindowManager.LayoutParams.FLAG_FULLSCREEN
        );

        // Asegurar que la ventana use todo el espacio disponible
        getWindow().getDecorView().setSystemUiVisibility(
            android.view.View.SYSTEM_UI_FLAG_FULLSCREEN |
            android.view.View.SYSTEM_UI_FLAG_HIDE_NAVIGATION |
            android.view.View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        );

        // Obtener datos del intent
        fullScreenPath = getIntent().getStringExtra("full_screen_path");
        userMessage = getIntent().getStringExtra("user_message");

        if (fullScreenPath == null) {
            Toast.makeText(this, "Error: No se encontró la captura", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }
        
        // Cargar la imagen capturada
        fullScreenBitmap = BitmapFactory.decodeFile(fullScreenPath);
        if (fullScreenBitmap == null) {
            Toast.makeText(this, "Error: No se pudo cargar la captura", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        // Debug: Log de dimensiones para diagnosticar el problema
        android.util.DisplayMetrics displayMetrics = new android.util.DisplayMetrics();
        getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
        Log.d(TAG, "Screen dimensions: " + displayMetrics.widthPixels + "x" + displayMetrics.heightPixels);
        Log.d(TAG, "Bitmap dimensions: " + fullScreenBitmap.getWidth() + "x" + fullScreenBitmap.getHeight());
        
        setupUI();
    }
    
    private void setupUI() {
        // Crear layout principal
        RelativeLayout mainLayout = new RelativeLayout(this);
        mainLayout.setBackgroundColor(Color.BLACK);

        // Asegurar que no haya padding o margin en el layout principal
        mainLayout.setPadding(0, 0, 0, 0);
        mainLayout.setFitsSystemWindows(false);
        
        // ImageView para mostrar la captura
        imageView = new ImageView(this);
        imageView.setImageBitmap(fullScreenBitmap);
        // Usar FIT_XY para estirar la imagen y llenar exactamente toda la pantalla
        imageView.setScaleType(ImageView.ScaleType.FIT_XY);
        imageView.setId(View.generateViewId());

        // Asegurar que no haya padding o margin
        imageView.setPadding(0, 0, 0, 0);
        imageView.setAdjustViewBounds(false);
        
        RelativeLayout.LayoutParams imageParams = new RelativeLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        );
        // Asegurar que la imagen esté alineada en la parte superior
        imageParams.addRule(RelativeLayout.ALIGN_PARENT_TOP);
        imageParams.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
        imageParams.setMargins(0, 0, 0, 0);

        mainLayout.addView(imageView, imageParams);
        
        // Overlay de selección
        selectionOverlay = new SelectionOverlay(this);
        RelativeLayout.LayoutParams overlayParams = new RelativeLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        );
        mainLayout.addView(selectionOverlay, overlayParams);
        
        // Botones de control
        addControlButtons(mainLayout);

        setContentView(mainLayout);

        // Aplicar animación de fade-in para una transición suave
        mainLayout.setAlpha(0f);
        mainLayout.animate()
            .alpha(1f)
            .setDuration(300)
            .setInterpolator(new android.view.animation.DecelerateInterpolator())
            .start();
    }
    
    private void addControlButtons(RelativeLayout mainLayout) {
        // Common size and styles for square buttons
        int buttonSize = 72; // Size in dp for square buttons (reduced by 20% from 90dp)
        int buttonSizePx = (int) (buttonSize * getResources().getDisplayMetrics().density);
        
        // Get screen width to calculate positions
        int screenWidth = getResources().getDisplayMetrics().widthPixels;
        
        // Crear posiciones relativas para los botones (en porcentajes del ancho de pantalla)
        // Posición del botón verde: 20% desde la izquierda
        // Posición del botón azul: 50% (centro)
        // Posición del botón rojo: 80% desde la izquierda (20% desde la derecha)
        int leftPos = screenWidth / 5;      // 20% del ancho
        int rightPos = screenWidth * 4 / 5; // 80% del ancho
        
        // Botón Capturar (Green check)
        Button captureButton = new Button(this);
        captureButton.setText("✓");
        captureButton.setBackgroundColor(Color.GREEN);
        captureButton.setTextColor(Color.WHITE);
        captureButton.setTextSize(24);
        captureButton.setOnClickListener(v -> captureSelectedArea());
        
        RelativeLayout.LayoutParams captureParams = new RelativeLayout.LayoutParams(
            buttonSizePx,
            buttonSizePx
        );
        captureParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        // Centrar el botón en su posición horizontal
        captureParams.leftMargin = leftPos - buttonSizePx / 2;
        captureParams.bottomMargin = 200; // 200dp from bottom
        
        // Botón de captura simple para galería (Purple - arriba del SS)
        Button simpleGalleryButton = new Button(this);
        simpleGalleryButton.setText("📷");
        simpleGalleryButton.setBackgroundColor(Color.parseColor("#9C27B0")); // Color morado
        simpleGalleryButton.setTextColor(Color.WHITE);
        simpleGalleryButton.setTextSize(24);
        simpleGalleryButton.setOnClickListener(v -> captureForGalleryOnly());

        RelativeLayout.LayoutParams simpleGalleryParams = new RelativeLayout.LayoutParams(
            buttonSizePx,
            buttonSizePx
        );
        simpleGalleryParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        simpleGalleryParams.addRule(RelativeLayout.CENTER_HORIZONTAL);
        simpleGalleryParams.bottomMargin = 400; // 400dp from bottom (bien separado del SS)

        // Botón SS (Captura completa - Blue)
        Button fullScreenButton = new Button(this);
        fullScreenButton.setText("SS");
        fullScreenButton.setBackgroundColor(Color.BLUE);
        fullScreenButton.setTextColor(Color.WHITE);
        fullScreenButton.setTextSize(24);
        fullScreenButton.setOnClickListener(v -> captureFullScreen());

        RelativeLayout.LayoutParams fullScreenParams = new RelativeLayout.LayoutParams(
            buttonSizePx,
            buttonSizePx
        );
        fullScreenParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        fullScreenParams.addRule(RelativeLayout.CENTER_HORIZONTAL);
        fullScreenParams.bottomMargin = 200; // 200dp from bottom
        
        // Botón Cancelar (Red X)
        Button cancelButton = new Button(this);
        cancelButton.setText("✕");
        cancelButton.setBackgroundColor(Color.RED);
        cancelButton.setTextColor(Color.WHITE);
        cancelButton.setTextSize(24);
        cancelButton.setOnClickListener(v -> {
            // Notificar al FloatingService que la captura fue cancelada
            Intent broadcastIntent = new Intent(FloatingService.ACTION_RESTORE_CHAT_WINDOW);
            LocalBroadcastManager.getInstance(this).sendBroadcast(broadcastIntent);
            Log.d(TAG, "Sent LOCAL broadcast for capture cancellation.");

            // Intentar minimizar la tarea actual (volver al Home)
            moveTaskToBack(true);

            cleanupAndFinish(true); // Con animación suave
        });
        
        RelativeLayout.LayoutParams cancelParams = new RelativeLayout.LayoutParams(
            buttonSizePx,
            buttonSizePx
        );
        cancelParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        // Centrar el botón en su posición horizontal
        cancelParams.leftMargin = rightPos - buttonSizePx / 2;
        cancelParams.bottomMargin = 200; // 200dp from bottom
        
        mainLayout.addView(captureButton, captureParams);
        mainLayout.addView(simpleGalleryButton, simpleGalleryParams);
        mainLayout.addView(fullScreenButton, fullScreenParams);
        mainLayout.addView(cancelButton, cancelParams);
    }
    
    private void captureSelectedArea() {
        Rect selectedArea = selectionOverlay.getSelectedArea();
        
        if (selectedArea.width() < 50 || selectedArea.height() < 50) {
            Toast.makeText(this, "Selecciona un área más grande", Toast.LENGTH_SHORT).show();
            return;
        }
        
        Log.d(TAG, "Capturing area: " + selectedArea.toString());
        
        try {
            // Recortar el área seleccionada
            Bitmap croppedBitmap = Bitmap.createBitmap(
                fullScreenBitmap,
                selectedArea.left,
                selectedArea.top,
                selectedArea.width(),
                selectedArea.height()
            );
            
            // Guardar el área recortada
            String croppedPath = saveCroppedBitmap(croppedBitmap);
            
            if (croppedPath != null) {
                // Enviar al FloatingService
                sendToFloatingService(croppedPath);
                croppedBitmap.recycle();
                cleanupAndFinish(true); // Con animación suave
            } else {
                Toast.makeText(this, "Error al guardar el área seleccionada", Toast.LENGTH_SHORT).show();
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error cropping area", e);
            Toast.makeText(this, "Error al recortar área: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
    
    private void captureForGalleryOnly() {
        Rect selectedArea = selectionOverlay.getSelectedArea();

        if (selectedArea.width() < 50 || selectedArea.height() < 50) {
            Toast.makeText(this, "Selecciona un área más grande", Toast.LENGTH_SHORT).show();
            return;
        }

        Log.d(TAG, "Capturing area for gallery only: " + selectedArea.toString());

        try {
            // Recortar el área seleccionada
            Bitmap croppedBitmap = Bitmap.createBitmap(
                fullScreenBitmap,
                selectedArea.left,
                selectedArea.top,
                selectedArea.width(),
                selectedArea.height()
            );

            // Guardar directamente en galería
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss", java.util.Locale.getDefault()).format(new java.util.Date());
            boolean saved = saveImageToGallery(croppedBitmap, "FloatingAI_" + timestamp);

            if (saved) {
                Toast.makeText(this, "Captura guardada en galería", Toast.LENGTH_SHORT).show();
                croppedBitmap.recycle();

                // Volver a donde estaba antes sin abrir el chat
                moveTaskToBack(true);
                cleanupAndFinish(true); // Con animación suave
            } else {
                Toast.makeText(this, "Error al guardar en galería", Toast.LENGTH_SHORT).show();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error capturing area for gallery", e);
            Toast.makeText(this, "Error al capturar área: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void captureFullScreen() {
        Log.d(TAG, "Capturing full screen");

        try {
            // Crear una copia del bitmap de pantalla completa
            Bitmap fullScreenCopy = fullScreenBitmap.copy(fullScreenBitmap.getConfig(), false);

            // Guardar la captura completa
            String fullScreenPath = saveFullScreenBitmap(fullScreenCopy);

            if (fullScreenPath != null) {
                // Enviar al FloatingService
                sendToFloatingService(fullScreenPath);
                fullScreenCopy.recycle();
                cleanupAndFinish(true); // Con animación suave
            } else {
                Toast.makeText(this, "Error al guardar la captura completa", Toast.LENGTH_SHORT).show();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error capturing full screen", e);
            Toast.makeText(this, "Error al capturar pantalla completa: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
    
    private String saveCroppedBitmap(Bitmap bitmap) {
        try {
            String filename = "cropped_" + System.currentTimeMillis() + ".jpg";
            FileOutputStream fos = openFileOutput(filename, MODE_PRIVATE);
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, fos);
            fos.close();
            
            File file = new File(getFilesDir(), filename);
            return file.getAbsolutePath();
        } catch (Exception e) {
            Log.e(TAG, "Error saving cropped bitmap", e);
            return null;
        }
    }
    
    private String saveFullScreenBitmap(Bitmap bitmap) {
        try {
            String filename = "fullscreen_" + System.currentTimeMillis() + ".jpg";
            FileOutputStream fos = openFileOutput(filename, MODE_PRIVATE);
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, fos);
            fos.close();
            
            File file = new File(getFilesDir(), filename);
            return file.getAbsolutePath();
        } catch (Exception e) {
            Log.e(TAG, "Error saving full screen bitmap", e);
            return null;
        }
    }
    
    private void sendToFloatingService(String screenshotPath) {
        Intent intent = new Intent(this, FloatingService.class);
        intent.setAction(FloatingService.ACTION_IMAGE_CAPTURED_FOR_CHAT);
        intent.putExtra("screenshot_path", screenshotPath);
        intent.putExtra("save_to_gallery", true);
        startService(intent);

        moveTaskToBack(true);
    }

    /**
     * Guarda una imagen en la galería del dispositivo.
     * @param bitmap La imagen a guardar
     * @param title El título para la imagen
     * @return true si se guardó exitosamente, false de lo contrario
     */
    private boolean saveImageToGallery(Bitmap bitmap, String title) {
        if (bitmap == null) {
            Log.e(TAG, "saveImageToGallery: bitmap is null");
            return false;
        }

        try {
            ContentResolver contentResolver = getContentResolver();

            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
                ContentValues values = new ContentValues();
                values.put(android.provider.MediaStore.Images.Media.DISPLAY_NAME, title + ".jpg");
                values.put(android.provider.MediaStore.Images.Media.MIME_TYPE, "image/jpeg");
                values.put(android.provider.MediaStore.Images.Media.RELATIVE_PATH, android.os.Environment.DIRECTORY_PICTURES + "/Screenshots");
                values.put(android.provider.MediaStore.Images.Media.IS_PENDING, 1);

                Uri imageUri = contentResolver.insert(android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);

                if (imageUri != null) {
                    try (java.io.OutputStream outputStream = contentResolver.openOutputStream(imageUri)) {
                        bitmap.compress(Bitmap.CompressFormat.JPEG, 95, outputStream);
                    }

                    values.clear();
                    values.put(android.provider.MediaStore.Images.Media.IS_PENDING, 0);
                    contentResolver.update(imageUri, values, null, null);

                    Log.d(TAG, "Image saved to gallery: " + title);
                    return true;
                }
            } else {
                // Para versiones anteriores a Android 10
                if (checkSelfPermission(android.Manifest.permission.WRITE_EXTERNAL_STORAGE) != android.content.pm.PackageManager.PERMISSION_GRANTED) {
                    Log.e(TAG, "No permission to write external storage");
                    return false;
                }

                String fileName = title + ".jpg";
                File directory = new File(android.os.Environment.getExternalStoragePublicDirectory(
                        android.os.Environment.DIRECTORY_PICTURES), "Screenshots");

                if (!directory.exists()) {
                    directory.mkdirs();
                }

                File file = new File(directory, fileName);
                try (java.io.FileOutputStream out = new java.io.FileOutputStream(file)) {
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 95, out);
                }

                // Notificar a la galería que se ha añadido una imagen
                Intent mediaScanIntent = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
                mediaScanIntent.setData(Uri.fromFile(file));
                sendBroadcast(mediaScanIntent);

                Log.d(TAG, "Image saved to gallery: " + file.getAbsolutePath());
                return true;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error saving image to gallery", e);
            return false;
        }

        return false;
    }
    
    private void cleanupAndFinish() {
        cleanupAndFinish(false); // Sin animación por defecto
    }

    private void cleanupAndFinish(boolean withAnimation) {
        if (withAnimation) {
            // Animación de fade-out antes de cerrar
            View rootView = findViewById(android.R.id.content);
            if (rootView != null) {
                rootView.animate()
                    .alpha(0f)
                    .setDuration(200)
                    .setInterpolator(new android.view.animation.AccelerateInterpolator())
                    .withEndAction(this::performCleanupAndFinish)
                    .start();
                return;
            }
        }
        performCleanupAndFinish();
    }

    private void performCleanupAndFinish() {
        // Limpiar archivo de captura completa
        try {
            File fullScreenFile = new File(fullScreenPath);
            if (fullScreenFile.exists()) {
                fullScreenFile.delete();
                Log.d(TAG, "Full screen file deleted");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error deleting full screen file", e);
        }

        if (fullScreenBitmap != null && !fullScreenBitmap.isRecycled()) {
            fullScreenBitmap.recycle();
        }

        finish();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        cleanupAndFinish();
    }
    
    // Clase para el overlay de selección
    private class SelectionOverlay extends View {
        private Paint borderPaint;
        private Paint fillPaint;
        private Paint dimPaint;
        private RectF selectedArea;
        private boolean isDragging = false;
        private int dragHandle = -1; // 0=top-left, 1=top-right, 2=bottom-left, 3=bottom-right, 4=move
        private float lastTouchX, lastTouchY;
        private int handleSize = 60;
        
        public SelectionOverlay(Activity context) {
            super(context);
            init();
        }
        
        private void init() {
            borderPaint = new Paint();
            borderPaint.setColor(Color.CYAN);
            borderPaint.setStyle(Paint.Style.STROKE);
            borderPaint.setStrokeWidth(4);
            
            fillPaint = new Paint();
            fillPaint.setColor(Color.argb(30, 0, 255, 255));
            fillPaint.setStyle(Paint.Style.FILL);
            
            dimPaint = new Paint();
            dimPaint.setColor(Color.argb(100, 0, 0, 0));
            dimPaint.setStyle(Paint.Style.FILL);
            
            // Área inicial (centro de la pantalla, 1/3 del tamaño)
            int screenWidth = getResources().getDisplayMetrics().widthPixels;
            int screenHeight = getResources().getDisplayMetrics().heightPixels;
            
            int width = screenWidth / 2;
            int height = screenHeight / 2;
            int left = (screenWidth - width) / 2;
            int top = (screenHeight - height) / 2;
            
            selectedArea = new RectF(left, top, left + width, top + height);
        }
        
        @Override
        protected void onDraw(Canvas canvas) {
            super.onDraw(canvas);
            
            // Dibujar área oscurecida alrededor de la selección
            canvas.drawRect(0, 0, getWidth(), selectedArea.top, dimPaint);
            canvas.drawRect(0, selectedArea.top, selectedArea.left, selectedArea.bottom, dimPaint);
            canvas.drawRect(selectedArea.right, selectedArea.top, getWidth(), selectedArea.bottom, dimPaint);
            canvas.drawRect(0, selectedArea.bottom, getWidth(), getHeight(), dimPaint);
            
            // Dibujar área seleccionada
            canvas.drawRect(selectedArea, fillPaint);
            canvas.drawRect(selectedArea, borderPaint);
            
            // Dibujar handles de redimensionamiento
            drawHandle(canvas, selectedArea.left, selectedArea.top); // top-left
            drawHandle(canvas, selectedArea.right, selectedArea.top); // top-right
            drawHandle(canvas, selectedArea.left, selectedArea.bottom); // bottom-left
            drawHandle(canvas, selectedArea.right, selectedArea.bottom); // bottom-right
        }
        
        private void drawHandle(Canvas canvas, float x, float y) {
            Paint handlePaint = new Paint();
            handlePaint.setColor(Color.CYAN);
            handlePaint.setStyle(Paint.Style.FILL);
            
            canvas.drawCircle(x, y, 15, handlePaint);
            
            handlePaint.setColor(Color.WHITE);
            canvas.drawCircle(x, y, 10, handlePaint);
        }
        
        @Override
        public boolean onTouchEvent(MotionEvent event) {
            float x = event.getX();
            float y = event.getY();
            
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    dragHandle = getHandleAt(x, y);
                    if (dragHandle != -1) {
                        isDragging = true;
                        lastTouchX = x;
                        lastTouchY = y;
                        return true;
                    }
                    break;
                    
                case MotionEvent.ACTION_MOVE:
                    if (isDragging && dragHandle != -1) {
                        updateSelectedArea(x, y);
                        invalidate();
                        return true;
                    }
                    break;
                    
                case MotionEvent.ACTION_UP:
                    isDragging = false;
                    dragHandle = -1;
                    break;
            }
            
            return super.onTouchEvent(event);
        }
        
        private int getHandleAt(float x, float y) {
            // Verificar handles de esquinas
            if (Math.abs(x - selectedArea.left) < handleSize && Math.abs(y - selectedArea.top) < handleSize) {
                return 0; // top-left
            }
            if (Math.abs(x - selectedArea.right) < handleSize && Math.abs(y - selectedArea.top) < handleSize) {
                return 1; // top-right
            }
            if (Math.abs(x - selectedArea.left) < handleSize && Math.abs(y - selectedArea.bottom) < handleSize) {
                return 2; // bottom-left
            }
            if (Math.abs(x - selectedArea.right) < handleSize && Math.abs(y - selectedArea.bottom) < handleSize) {
                return 3; // bottom-right
            }
            
            // Verificar si está dentro del área para mover
            if (selectedArea.contains(x, y)) {
                return 4; // move
            }
            
            return -1;
        }
        
        private void updateSelectedArea(float x, float y) {
            float deltaX = x - lastTouchX;
            float deltaY = y - lastTouchY;
            
            switch (dragHandle) {
                case 0: // top-left
                    selectedArea.left = Math.max(0, Math.min(selectedArea.left + deltaX, selectedArea.right - 100));
                    selectedArea.top = Math.max(0, Math.min(selectedArea.top + deltaY, selectedArea.bottom - 100));
                    break;
                case 1: // top-right
                    selectedArea.right = Math.min(getWidth(), Math.max(selectedArea.right + deltaX, selectedArea.left + 100));
                    selectedArea.top = Math.max(0, Math.min(selectedArea.top + deltaY, selectedArea.bottom - 100));
                    break;
                case 2: // bottom-left
                    selectedArea.left = Math.max(0, Math.min(selectedArea.left + deltaX, selectedArea.right - 100));
                    selectedArea.bottom = Math.min(getHeight(), Math.max(selectedArea.bottom + deltaY, selectedArea.top + 100));
                    break;
                case 3: // bottom-right
                    selectedArea.right = Math.min(getWidth(), Math.max(selectedArea.right + deltaX, selectedArea.left + 100));
                    selectedArea.bottom = Math.min(getHeight(), Math.max(selectedArea.bottom + deltaY, selectedArea.top + 100));
                    break;
                case 4: // move
                    float newLeft = selectedArea.left + deltaX;
                    float newTop = selectedArea.top + deltaY;
                    float newRight = selectedArea.right + deltaX;
                    float newBottom = selectedArea.bottom + deltaY;
                    
                    if (newLeft >= 0 && newRight <= getWidth()) {
                        selectedArea.left = newLeft;
                        selectedArea.right = newRight;
                    }
                    if (newTop >= 0 && newBottom <= getHeight()) {
                        selectedArea.top = newTop;
                        selectedArea.bottom = newBottom;
                    }
                    break;
            }
            
            lastTouchX = x;
            lastTouchY = y;
        }
        
        public Rect getSelectedArea() {
            return new Rect(
                (int) selectedArea.left,
                (int) selectedArea.top,
                (int) selectedArea.right,
                (int) selectedArea.bottom
            );
        }
    }
} 